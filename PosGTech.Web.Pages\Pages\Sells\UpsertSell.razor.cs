using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using MudBlazor;
using MudExtensions;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Sells;
using PosGTech.ModelsDTO.ShopSettings;
using PosGTech.ModelsDTO.StoreItem;
using PosGTech.ModelsDTO.StoreItemExp;
using PosGTech.ModelsDTO.Stores;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.Web.Pages.Pages.Clients;
using PosGTech.Web.Pages.Pages.Receipts;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Sells;

public partial class UpsertSell
{
    [CascadingParameter] MudDialogInstance MudDialog { get; set; }
    [Parameter]
    public Guid id { get; set; }
    [Inject]
    IJSRuntime _iJSRuntime { get; set; }
    [Inject]
    IGRepository<SellDTO> _sell { get; set; }
    [Inject]
    IGRepository<StoreCMDTO> _store { get; set; }
    [Inject]
    IGRepository<StoreItemDTO> _item { get; set; }
    [Inject]
    IGRepository<ClientCMDTO> _client { get; set; }
    [Inject]
    IDialogService DialogService { get; set; }
    [Inject]
    IGRepository<UserTreasuryCMDTO> _userTreasury { get; set; }
    [Inject]
    IGRepository<SellsNumDTO> _sellsNum { get; set; }
    [Inject]
    AuthenticationStateProvider _auth { get; set; }
    [Inject]
    IInventoryValidationService _inventoryValidation { get; set; }
    [Inject]
    IErrorLoggingService _errorLogging { get; set; }
    [Inject]
    IGRepository<ShopSettingsDTO> _shopSettings { get; set; }
    [Inject]
    NavigationManager Navigation { get; set; }
    //====================================================================================================
    SellDTO _Sell { get; set; } = new();
    List<StoreCMDTO> stores = new List<StoreCMDTO>();
    List<StoreItemDTO> items = new List<StoreItemDTO>();
    List<ClientCMDTO> clients = new List<ClientCMDTO>();
    List<UserTreasuryCMDTO> userTreasuries = new List<UserTreasuryCMDTO>();
    List<SellsNumDTO> SellsNum = new List<SellsNumDTO>();

    // متغيرات حالة التحميل للأزرار
    private bool _isLoading = false;
    private bool _isSaveLoading = false;
    private bool _isPrintLoading = false;
    private bool _isDeleteLoading = false;
    private bool _isNewLoading = false;
    private bool _isReceiptLoading = false;

    // متغيرات الطباعة وإعدادات المتجر
    private ShopSettingsDTO? _defaultShopSettings;
    private bool _isPrinting = false;
    private string _printButtonText = "طباعة الفاتورة";

    // طرق مساعدة لإدارة حالة التحميل
    private void SetLoadingState(bool isLoading)
    {
        _isLoading = isLoading;
        StateHasChanged();
    }

    private void SetButtonLoadingState(string buttonType, bool isLoading)
    {
        switch (buttonType.ToLower())
        {
            case "save":
                _isSaveLoading = isLoading;
                break;
            case "print":
                _isPrintLoading = isLoading;
                break;
            case "delete":
                _isDeleteLoading = isLoading;
                break;
            case "new":
                _isNewLoading = isLoading;
                break;
            case "receipt":
                _isReceiptLoading = isLoading;
                break;
        }
        _isLoading = _isSaveLoading || _isPrintLoading || _isDeleteLoading || _isNewLoading || _isReceiptLoading;
        StateHasChanged();
    }

    private void ShowSuccessMessage(string message = "تمت العملية بنجاح")
    {
        _snackbar.Add(message, Severity.Success);
    }

    private void ShowErrorMessage(string message = "حدث خطأ أثناء تنفيذ العملية")
    {
        _snackbar.Add(message, Severity.Error);
    }
    //====================================================================================================
    SellItemDTO? selectedStoreItem { get; set; } = new SellItemDTO() { StoreItemExp = new StoreItemExpDTO() };
    DateTime? _dateSell = DateTime.Now;
    MudComboBox<StoreItemForSellDTO> ItemForAdd { get; set; }
    MudMessageBox mbox { get; set; }
    string _message;
    bool _isDeleteMessage = false;
    MudTextField<int> InvoiceNo { get; set; }

    // Available quantity display properties
    private string _availableQuantityDisplay = "غير متاح";
    public string AvailableQuantityDisplay
    {
        get => _availableQuantityDisplay;
        private set
        {
            _availableQuantityDisplay = value;
            StateHasChanged();
        }
    }

    /// <summary>
    /// Updates the available quantity display based on selected item, unit, and batch
    /// </summary>
    private async Task UpdateAvailableQuantityDisplay()
    {
        try
        {
            if (selectedStoreItem?.StoreItemExp?.StoreItem?.Item == null ||
                selectedStoreItem?.ItemUnit == null)
            {
                AvailableQuantityDisplay = "غير متاح";
                return;
            }

            var storeItemExp = selectedStoreItem.StoreItemExp;
            var itemUnit = selectedStoreItem.ItemUnit;

            // Calculate available quantity based on unit type
            decimal availableQuantity;

            if (itemUnit.IsBasicUnit)
            {
                // For basic unit, show quantity as is
                availableQuantity = storeItemExp.Quantity;
            }
            else if (itemUnit.IsBigger)
            {
                // For bigger units (like boxes), divide available quantity by unit quantity
                // Example: 100 pieces available, box contains 10 pieces = 10 boxes available
                availableQuantity = storeItemExp.Quantity / itemUnit.Quantity;
            }
            else
            {
                // For smaller units, multiply by unit quantity
                // Example: 100 pieces available, half-piece unit = 200 half-pieces available
                availableQuantity = storeItemExp.Quantity * itemUnit.Quantity;
            }

            if (availableQuantity > 0)
            {
                var unitName = itemUnit.Unit?.Name ?? "";
                AvailableQuantityDisplay = $"{availableQuantity:0.##} {unitName}";
            }
            else
            {
                AvailableQuantityDisplay = "غير متاح";
            }
        }
        catch (Exception ex)
        {
            // Log error and show fallback message
            await _errorLogging.LogExceptionAsync(ex, "Error updating available quantity display", new Dictionary<string, object>
            {
                ["ItemId"] = selectedStoreItem?.StoreItemExp?.StoreItem?.Item?.Id.ToString() ?? "Unknown",
                ["UnitId"] = selectedStoreItem?.ItemUnit?.Unit?.Id.ToString() ?? "Unknown",
                ["BatchId"] = selectedStoreItem?.StoreItemExp?.Id.ToString() ?? "Unknown",
                ["IsBasicUnit"] = selectedStoreItem?.ItemUnit?.IsBasicUnit.ToString() ?? "Unknown",
                ["IsBigger"] = selectedStoreItem?.ItemUnit?.IsBigger.ToString() ?? "Unknown",
                ["UnitQuantity"] = selectedStoreItem?.ItemUnit?.Quantity.ToString() ?? "Unknown",
                ["AvailableQuantity"] = selectedStoreItem?.StoreItemExp?.Quantity.ToString() ?? "Unknown"
            });
            AvailableQuantityDisplay = "خطأ في حساب الكمية";
        }
    }
    public async void keydownForm(KeyboardEventArgs args)
    {
        if (args.Key == "F2") await Upsert();
        if (args.Key == "F8") await NewSell();
    }
    public async Task KeyDownInvoice(KeyboardEventArgs args)
    {
        // نفترض وجود خاصية id التي تحمل معرف الفاتورة الحالية.
        // وتوجد مجموعة SellsNum تحتوي على الفواتير بخصائص InvoiceNo و Id.

        if (args.Key == "Enter")
        {
            // التأكد من أن القيمة محدثة قبل البحث
            StateHasChanged();
            await Task.Delay(100); // إعطاء وقت قصير لتحديث القيمة

            // جلب الفاتورة بناءً على رقم الفاتورة المدخل
            var sellFound = SellsNum.FirstOrDefault(x => x.InvoiceNo == _Sell.InvoiceNo);
            if (sellFound == null)
            {
                _snackbar.Add("الفاتورة غير موجودة", Severity.Error);
                return;
            }
            await GetSell(sellFound.Id);
            await InvoiceNo.FocusAsync();
        }
        else if (args.Key == "ArrowRight")
        {
            // عند الضغط على السهم اليمين:
            // إذا كان id ليس Guid.Empty نحاول جلب الفاتورة السابقة.
            if (id != Guid.Empty)
            {
                int currentIndex = SellsNum.FindIndex(x => x.Id == id);
                if (currentIndex > 0)
                {
                    // الفاتورة السابقة تكون العنصر الذي قبله في القائمة
                    var previousInvoice = SellsNum[currentIndex - 1];
                    await GetSell(previousInvoice.Id);
                }
                else
                {
                    _snackbar.Add("لا توجد فاتورة سابقة", Severity.Error);
                }
            }
            else
            {
                // إذا كان id يساوي Guid.Empty، نجلب آخر فاتورة إن وجدت
                if (SellsNum.Any())
                {
                    var lastInvoice = SellsNum.Last();
                    await GetSell(lastInvoice.Id);
                }
                else
                {
                    _snackbar.Add("لا توجد فواتير", Severity.Error);
                }
            }
            await InvoiceNo.FocusAsync();
        }
        else if (args.Key == "ArrowLeft")
        {
            // عند الضغط على السهم اليسار:
            // إذا كان id ليس Guid.Empty نحاول جلب الفاتورة اللاحقة.
            if (id != Guid.Empty)
            {
                int currentIndex = SellsNum.FindIndex(x => x.Id == id);
                if (currentIndex >= 0 && currentIndex < SellsNum.Count - 1)
                {
                    // الفاتورة اللاحقة تكون العنصر الذي يلي الفاتورة الحالية
                    var nextInvoice = SellsNum[currentIndex + 1];
                    await GetSell(nextInvoice.Id);
                }
                else
                {
                    _snackbar.Add("لا توجد فاتورة لاحقة", Severity.Error);
                }
            }
            else
            {
                // إذا كان id يساوي Guid.Empty، نجلب آخر فاتورة إن وجدت
                if (SellsNum.Any())
                {
                    var lastInvoice = SellsNum.Last();
                    await GetSell(lastInvoice.Id);
                }
                else
                {
                    _snackbar.Add("لا توجد فواتير", Severity.Error);
                }
            }
            await InvoiceNo.FocusAsync();
        }
    }
    async Task GetSellsNum()
    {
        var res = await _sellsNum.GetAll("Sells/getAllSellsNum");
        if (res.response == null) SellsNum = res.list.ToList();
    }
    protected override async Task OnInitializedAsync()
    {
        var resStore = await _store.GetAll("Stores/getAllStoresCMB");
        if (resStore.response == null) stores = resStore.list.ToList();
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
        await GetClients();
        await GetSellsNum();
        await LoadDefaultShopSettings();
        if (id != Guid.Empty) await GetSell(id);
        else
        {
            _Sell.InvoiceNo = SellsNum.Any() ? SellsNum.Max(x => x.InvoiceNo) + 1 : 1;
            var auth = await _auth.GetAuthenticationStateAsync();
            var resUserTreasuries = await _userTreasury.GetAll($"Users/getAllTreasuriesForUser/{auth.User.Claims.First(x => x.Type == "id").Value}");
            if (resUserTreasuries.response == null) userTreasuries = resUserTreasuries.list.ToList();
            else
            {
                _snackbar.Add("خطأ في الاتصال", Severity.Error);
                MudDialog.Cancel();
            }
        }
    }
    async Task GetSell(Guid sellId)
    {
        id = sellId;
        var res = await _sell.GetByIdAsync("Sells/getSellById", id);
        if (res.response == null)
        {
            _Sell = res.model;
            _dateSell = _Sell.Date.ToDateTime(time: TimeOnly.MinValue);
            StateHasChanged();
            await ChangeStore(_Sell.StoreId);
        }
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
    }
    async Task ChangeStore(Guid? id)
    {
        _Sell.StoreId = id;

        // مسح الأصناف المحددة عند تغيير المستودع
        selectedStoreItem = new SellItemDTO() { StoreItemExp = new StoreItemExpDTO() };
        if (ItemForAdd != null)
        {
            ItemForAdd.Text = null;
        }

        if (id is not null)
        {
            await GetItems((Guid)id);
        }
        else
        {
            // مسح قائمة الأصناف إذا لم يتم اختيار مستودع
            items.Clear();
        }

        StateHasChanged();
    }

    async Task GetItems(Guid id)
    {
        // مسح قائمة الأصناف قبل تحميل الأصناف الجديدة
        items.Clear();

        var resItem = await _item.GetAll("StoreItems/StoreItemsById/" + id);
        if (resItem.response == null)
        {
            items = resItem.list.ToList();
        }
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
        StateHasChanged();
    }
    async Task GetClients()
    {

        var resClient = await _client.GetAll("Clients/getAllCustomers");
        if (resClient.response == null) clients = resClient.list.ToList();
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
        }
    }
    Func<ClientCMDTO, string, string, bool> SearchClientFunc => SearchClient;

    private bool SearchClient(ClientCMDTO cMDTO, string arg2, string arg3)
    {
        return cMDTO.Name.Contains(arg3, StringComparison.InvariantCultureIgnoreCase) || string.IsNullOrEmpty(arg3);
    }
    async void AddNewClient()
    {
        var parameters = new DialogParameters<UpsertClient>();
        parameters.Add(x => x.id, Guid.Empty);
        var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
        var result = await DialogService.Show<UpsertClient>("إضافة العميل", parameters, options).Result;
        if ((string?)result.Data != null)
        {
            await GetClients();
            _Sell.Client = clients.First(x => x.Name == (string)result.Data);
            StateHasChanged();
        }
    }
    /// <summary>
    /// إختيار الصنف عن طريق الباركود
    /// </summary>
    /// <param name="e"></param>
    /// <returns></returns>
    public async Task SelectItem(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            if (ItemForAdd.Text == "" || ItemForAdd.Text is null)
                return;
            if (items.Any(x => x.Item.Name.Contains(ItemForAdd.Text, StringComparison.InvariantCultureIgnoreCase) || x.Item.ItemNums?.Any(x => x.Barcode == ItemForAdd.Text) == true))
            {
                await ChangeItem(items.Where(x => x.Item.Name.Contains(ItemForAdd.Text, StringComparison.InvariantCultureIgnoreCase) || x.Item.ItemNums?.Any(x => x.Barcode == ItemForAdd.Text) == true).Select(x => new StoreItemForSellDTO() { Item = new ItemCMDTO() { Id = x.Item.Id, Name = x.Item.Name }, StoreId = x.StoreId }).First());

            }
            else await ChangeItem(null);
            StateHasChanged();
        }

    }
    private async Task ChangeItem(StoreItemForSellDTO? storeItem)
    {
        selectedStoreItem.ItemUnit = null;
        selectedStoreItem.SalePrice = 0;
        if (storeItem != null)
        {
            var selectedItem = items.FirstOrDefault(x => x.Item.Id == storeItem.Item.Id);
            if (selectedItem is not null)
            {
                var basicUnit = selectedItem.Item.ItemUnits.FirstOrDefault(x => x.IsBasicUnit);
                if (basicUnit is not null)
                {
                    selectedStoreItem.ItemUnit = basicUnit;
                    // استخدام سعر الوحدة الأساسية مباشرة
                    selectedStoreItem.SalePrice = basicUnit.SalePrice;
                }

                selectedStoreItem.CostPrice = selectedItem.Item.CostPrice;
                selectedStoreItem.StoreItemExp = selectedItem.StoreItemExps.FirstOrDefault();

                if (selectedStoreItem.StoreItemExp is not null)
                {
                    selectedStoreItem.StoreItemExp.StoreItem = new StoreItemForSellDTO() { Item = storeItem.Item, StoreId = storeItem.StoreId };

                    // Check for low stock warning
                    await CheckLowStockWarning(selectedItem, selectedStoreItem.StoreItemExp.Id);
                }
            }
        }

        // Update available quantity display
        await UpdateAvailableQuantityDisplay();
    }

    private async Task CheckLowStockWarning(StoreItemDTO storeItem, Guid expId)
    {
        try
        {
            var lowStockResult = await _inventoryValidation.CheckLowStockAsync(storeItem, expId);
            if (lowStockResult.HasLowStock)
            {
                _snackbar.Add(lowStockResult.WarningMessage, Severity.Warning);
            }
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "CheckLowStockWarning",
                new Dictionary<string, object>
                {
                    ["ItemId"] = storeItem?.Item?.Id.ToString() ?? "Unknown",
                    ["ExpId"] = expId.ToString()
                });
        }
    }

    Func<StoreItemForSellDTO, string, string, bool> SearchItemFunc => SearchItem;

    private bool SearchItem(StoreItemForSellDTO cMDTO, string arg2, string arg3)
    {
        return cMDTO.Item.Name.Contains(arg3, StringComparison.InvariantCultureIgnoreCase) || string.IsNullOrEmpty(arg3);
    }

    private async Task ChangeItemUnit(ItemUnitDTO? unit)
    {
        selectedStoreItem.ItemUnit = unit;
        if (unit is not null && selectedStoreItem?.StoreItemExp?.StoreItem?.Item is not null)
        {
            // الحصول على سعر الوحدة الأساسية
            var selectedItem = items.FirstOrDefault(x => x.Item.Id == selectedStoreItem.StoreItemExp.StoreItem.Item.Id);
            if (selectedItem is not null)
            {
                var basicUnit = selectedItem.Item.ItemUnits.FirstOrDefault(x => x.IsBasicUnit);
                if (basicUnit is not null)
                {
                    // حساب السعر الصحيح بناءً على نوع الوحدة المختارة
                    selectedStoreItem.SalePrice = ItemExtensions.GetPriceUnitDTO(unit, basicUnit.SalePrice);
                }
                else
                {
                    selectedStoreItem.SalePrice = 0;
                }
            }
            else
            {
                selectedStoreItem.SalePrice = 0;
            }
        }
        else
        {
            selectedStoreItem.SalePrice = 0;
        }

        // Update available quantity display
        await UpdateAvailableQuantityDisplay();
    }
    private async Task ChangeExp(Guid expId)
    {
        var StoreItem = selectedStoreItem?.StoreItemExp?.StoreItem;
        selectedStoreItem.StoreItemExp = items.SelectMany(x => x.StoreItemExps).FirstOrDefault(x => x.Id == expId);
        if (selectedStoreItem.StoreItemExp is not null && StoreItem is not null)
        {
            selectedStoreItem.StoreItemExp.StoreItem = StoreItem;
        }

        // Update available quantity display
        await UpdateAvailableQuantityDisplay();
    }

    private async Task ChangeExp(StoreItemExpDTO? exp)
    {
        var StoreItem = selectedStoreItem?.StoreItemExp?.StoreItem;
        selectedStoreItem.StoreItemExp = exp;
        if (selectedStoreItem.StoreItemExp is not null && StoreItem is not null)
        {
            selectedStoreItem.StoreItemExp.StoreItem = StoreItem;
        }

        // Update available quantity display
        await UpdateAvailableQuantityDisplay();
    }
    private void ChangeQte(decimal quantity)
    {
        if (quantity <= 0)
        {
            selectedStoreItem.Quantity = decimal.One;
            StateHasChanged();
            return;
        }

        if (selectedStoreItem?.StoreItemExp?.StoreItem?.Item is not null && selectedStoreItem.StoreItemExp.Id != Guid.Empty)
        {
            var selectedItem = items.FirstOrDefault(x => x.Item.Id == selectedStoreItem.StoreItemExp.StoreItem.Item.Id);
            var selectedExp = selectedItem?.StoreItemExps.FirstOrDefault(x => x.Id == selectedStoreItem.StoreItemExp.Id);

            if (selectedExp is not null && selectedStoreItem.ItemUnit is not null)
            {
                decimal qte = ItemExtensions.GetQuantityUnitDTO(selectedStoreItem.ItemUnit, selectedExp.Quantity);
                if (quantity > qte)
                {
                    selectedStoreItem.Quantity = qte;
                    _snackbar.Add("الكمية المدخلة اكبر من الكمية المتاحة", Severity.Error);
                    StateHasChanged();
                    return;
                }
            }
        }

        selectedStoreItem.Quantity = quantity;
        StateHasChanged();
    }
    async Task AddSellItem()
    {
        // التحقق من اختيار المستودع أولاً
        if (_Sell.StoreId == null)
        {
            _snackbar.Add("الرجاء اختيار المستودع أولاً", Severity.Error);
            StateHasChanged();
            return;
        }

        if (selectedStoreItem?.StoreItemExp?.StoreItem?.Item is null)
        {
            _snackbar.Add("الرجاء اختيار صنف", Severity.Error);
            StateHasChanged();
            return;
        }

        // Validate the item before adding
        var itemValidation = await _inventoryValidation.ValidateSingleItemAsync(selectedStoreItem, items);
        if (!itemValidation.IsValid)
        {
            _snackbar.Add(itemValidation.ErrorMessage, Severity.Error);
            return;
        }
        //في حالة الصنف موجود في القائمة 
        if (_Sell.SellItemDTOs.Any(x => x.StoreItemExp?.Id == selectedStoreItem?.StoreItemExp?.Id && x.ItemUnit.Id == selectedStoreItem.ItemUnit.Id
                                        && x.SalePrice == selectedStoreItem.SalePrice))
        {
            _isDeleteMessage = false;
            _message = $" هذا الصنف {selectedStoreItem?.StoreItemExp.StoreItem.Item.Name} موجود هل تريد اضافة الكمية؟";
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                _Sell.SellItemDTOs.First(x => x.StoreItemExp?.Id == selectedStoreItem?.StoreItemExp?.Id && x.ItemUnit.Id == selectedStoreItem.ItemUnit.Id
                                        && x.SalePrice == selectedStoreItem.SalePrice)
                    .Quantity += selectedStoreItem.Quantity;


            }
            else return;

        }
        else
        {            //في حالة الصنف  غير موجود في القائمة 
            selectedStoreItem.SellId = _Sell.Id;
            _Sell.SellItemDTOs.Add(selectedStoreItem);
        }

        if (selectedStoreItem?.StoreItemExp?.StoreItem?.Item is not null && selectedStoreItem.StoreItemExp.Id != Guid.Empty && selectedStoreItem.ItemUnit is not null)
        {
            var selectedItem = items.FirstOrDefault(x => x.Item.Id == selectedStoreItem.StoreItemExp.StoreItem.Item.Id);
            var selectedExp = selectedItem?.StoreItemExps.FirstOrDefault(x => x.Id == selectedStoreItem.StoreItemExp.Id);
            if (selectedExp is not null)
            {
                selectedExp.Quantity -= ItemExtensions.GetQuantityUnitDTO(selectedStoreItem.ItemUnit, selectedStoreItem.Quantity);
            }
        }
        _Sell.Total = _Sell.SellItemDTOs.Sum(x => x.Quantity * x.SalePrice);
        selectedStoreItem = new SellItemDTO() { StoreItemExp = new StoreItemExpDTO() };
        ItemForAdd.Text = null;
        await ItemForAdd.FocusAsync();
        StateHasChanged();
        ChangeDiscount();
    }
    private void DeleteItem(SellItemDTO item)
    {
        if (item.ReturnQuantity > 0)
        {
            _snackbar.Add($"لا يمكن حذف الصنف بعد استرجاعه", Severity.Error); return;
        }
        _Sell.SellItemDTOs.Remove(item);

        if (item?.StoreItemExp?.StoreItem?.Item is not null && item.StoreItemExp.Id != Guid.Empty && item.ItemUnit is not null)
        {
            var selectedItem = items.FirstOrDefault(x => x.Item.Id == item.StoreItemExp.StoreItem.Item.Id);
            var selectedExp = selectedItem?.StoreItemExps.FirstOrDefault(x => x.Id == item.StoreItemExp.Id);
            if (selectedExp is not null)
            {
                selectedExp.Quantity += ItemExtensions.GetQuantityUnitDTO(item.ItemUnit, item.Quantity);
            }
        }

        _Sell.Total = _Sell.SellItemDTOs.Sum(x => x.SalePrice * x.Quantity);
        StateHasChanged();
        ChangeDiscount();
    }
    async Task AddReceipt(Guid financialId)
    {
        if (_isLoading) return; // منع التنفيذ المتعدد

        try
        {
            SetButtonLoadingState("receipt", true);

            var parameters = new DialogParameters<UpsertReceipt>();
            parameters.Add(x => x.id, Guid.Empty);
            parameters.Add(x => x.financialId, financialId);
            parameters.Add(x => x.Sell, new SellReceiptDTO() { Id = _Sell.Id, Client = _Sell.Client, FinalTotal = _Sell.FinalTotal, InvoiceNo = _Sell.InvoiceNo, Paid = _Sell.Paid });
            var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertReceipt>("إضافة الإيصال", parameters, options).Result;

            if ((bool?)result.Data == true)
            {
                await GetSell(id);
                StateHasChanged();
                ShowSuccessMessage("تم إضافة الإيصال بنجاح");
            }
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "AddReceipt",
                new Dictionary<string, object>
                {
                    ["SellId"] = _Sell.Id.ToString(),
                    ["FinancialId"] = financialId.ToString()
                });
            ShowErrorMessage("حدث خطأ أثناء إضافة الإيصال. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            SetButtonLoadingState("receipt", false);
        }
    }
    async Task Upsert()
    {
        if (_isLoading) return; // منع التنفيذ المتعدد

        try
        {
            SetButtonLoadingState("save", true);

            if (_Sell.SellItemDTOs.Count == 0)
            {
                ShowErrorMessage("يرجى اضافة اصناف");
                return;
            }
            if (_dateSell == null)
            {
                ShowErrorMessage("يرجى اختيار تاريخ");
                return;
            }
            if (_Sell.TreasuryId == null && _Sell.Paid > 0 && _Sell.Id == Guid.Empty)
            {
                ShowErrorMessage("يرجى اختيار الخزينة");
                return;
            }

            // Validate inventory before saving
            var inventoryValidation = await ValidateInventoryBeforeSave();
            if (!inventoryValidation.IsValid)
            {
                foreach (var errorMessage in inventoryValidation.ErrorMessages)
                {
                    ShowErrorMessage(errorMessage);
                }
                return;
            }

            _Sell.Date = DateOnly.FromDateTime(_dateSell.Value);
            _Sell.Total = _Sell.FinalTotal;
            ResponseVM response;

            if (id == Guid.Empty)
            {
                response = await _sell.Insert("Sells/insertSell", _Sell);
            }
            else
            {
                response = await _sell.Update("Sells/updateSell", _Sell, id);
            }

            if (response.State)
            {
                ShowSuccessMessage("تم الحفظ بنجاح");
                await GetSell(id == Guid.Empty ? Guid.Parse(response.Message) : id);
                await GetSellsNum();
            }
            else
            {
                ShowErrorMessage(response.Message);
            }
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "Upsert",
                new Dictionary<string, object>
                {
                    ["SellId"] = _Sell.Id.ToString(),
                    ["InvoiceNo"] = _Sell.InvoiceNo.ToString()
                });
            ShowErrorMessage("حدث خطأ أثناء حفظ الفاتورة. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            SetButtonLoadingState("save", false);
        }
    }

    private async Task<InventoryValidationResult> ValidateInventoryBeforeSave()
    {
        try
        {
            // Create a copy of items with restored quantities for validation
            var itemsForValidation = CreateItemsForValidation();
            return await _inventoryValidation.ValidateInventoryAsync(_Sell.SellItemDTOs, itemsForValidation);
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "ValidateInventoryBeforeSave");

            var errorResult = new InventoryValidationResult
            {
                IsValid = false,
                ErrorMessages = { "حدث خطأ أثناء التحقق من المخزون. يرجى المحاولة مرة أخرى." }
            };
            return errorResult;
        }
    }

    /// <summary>
    /// Creates a copy of items with quantities restored to original values for validation
    /// This is needed because the UI already deducted quantities when items were added to the sell
    /// </summary>
    private List<StoreItemDTO> CreateItemsForValidation()
    {
        // Create a deep copy of items
        var itemsForValidation = items.Select(item => new StoreItemDTO
        {
            Item = item.Item,
            StoreId = item.StoreId,
            StoreItemExps = item.StoreItemExps.Select(exp => new StoreItemExpDTO
            {
                Id = exp.Id,
                Quantity = exp.Quantity,
                Exp = exp.Exp,
                StoreItem = exp.StoreItem
            }).ToList()
        }).ToList();

        // Restore quantities that were deducted in the UI
        foreach (var sellItem in _Sell.SellItemDTOs)
        {
            var validationItem = itemsForValidation.FirstOrDefault(x => x.Item?.Id == sellItem.StoreItemExp?.StoreItem?.Item?.Id);
            var validationExp = validationItem?.StoreItemExps?.FirstOrDefault(x => x.Id == sellItem.StoreItemExp?.Id);

            if (validationExp != null && sellItem.ItemUnit != null)
            {
                // Add back the quantity that was deducted in the UI
                var deductedQuantity = ItemExtensions.GetQuantityUnitDTO(sellItem.ItemUnit, sellItem.Quantity);
                validationExp.Quantity += deductedQuantity;
            }
        }

        return itemsForValidation;
    }
    void ChangeDiscountValue(decimal discount)
    {
        _Sell.DiscountValue = discount;
        ChangeDiscount();
    }
    void ChangeIsDiscount(bool isDiscount)
    {
        _Sell.IsDiscountValue = isDiscount;
        ChangeDiscount();
    }
    void ChangeDiscount()
    {
        _Sell.FinalTotal = _Sell.Total - (_Sell.IsDiscountValue ? _Sell.DiscountValue : (_Sell.Total * _Sell.DiscountValue / 100));
        _Sell.SellItemDTOs.ForEach(x =>
        {
            x.SalePriceAfterDiscount = (((x.Quantity * x.SalePrice) / _Sell.Total) * _Sell.FinalTotal) / x.Quantity;
        });
        StateHasChanged();
    }
    void AddQuantity(SellItemDTO sellItem)
    {
        if (sellItem?.StoreItemExp?.StoreItem?.Item is not null && sellItem.StoreItemExp.Id != Guid.Empty && sellItem.ItemUnit is not null)
        {
            var selectedItem = items.FirstOrDefault(x => x.Item.Id == sellItem.StoreItemExp.StoreItem.Item.Id);
            var selectedExp = selectedItem?.StoreItemExps.FirstOrDefault(x => x.Id == sellItem.StoreItemExp.Id);

            if (selectedExp is not null)
            {
                decimal qte = ItemExtensions.GetQuantityUnitDTO(sellItem.ItemUnit, selectedExp.Quantity);
                if (sellItem.Quantity + 1 > qte)
                {
                    _snackbar.Add("الكمية المدخلة اكبر من الكمية المتاحة", Severity.Error);
                    return;
                }
                sellItem.Quantity += 1;
                selectedExp.Quantity -= ItemExtensions.GetQuantityUnitDTO(sellItem.ItemUnit, 1);
                StateHasChanged();
            }
        }
    }
    void RemoveQuantity(SellItemDTO sellItem)
    {
        if (sellItem.Quantity - 1 <= 0)
        {
            DeleteItem(sellItem);
            return;
        }

        sellItem.Quantity -= 1;

        if (sellItem?.StoreItemExp?.StoreItem?.Item is not null && sellItem.StoreItemExp.Id != Guid.Empty && sellItem.ItemUnit is not null)
        {
            var selectedItem = items.FirstOrDefault(x => x.Item.Id == sellItem.StoreItemExp.StoreItem.Item.Id);
            var selectedExp = selectedItem?.StoreItemExps.FirstOrDefault(x => x.Id == sellItem.StoreItemExp.Id);
            if (selectedExp is not null)
            {
                selectedExp.Quantity += ItemExtensions.GetQuantityUnitDTO(sellItem.ItemUnit, 1);
            }
        }

        StateHasChanged();
    }
    void Back() => MudDialog.Cancel();
    async Task NewSell()
    {
        if (_isLoading) return; // منع التنفيذ المتعدد

        try
        {
            SetButtonLoadingState("new", true);

            _Sell = new();
            _dateSell = DateTime.Now;
            id = new();
            _Sell.InvoiceNo = SellsNum.Any() ? SellsNum.Max(x => x.InvoiceNo) + 1 : 1;

            ShowSuccessMessage("تم إنشاء فاتورة جديدة");
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "NewSell");
            ShowErrorMessage("حدث خطأ أثناء إنشاء فاتورة جديدة. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            SetButtonLoadingState("new", false);
        }
    }
    async Task Delete(SellDTO obj)
    {
        if (_isLoading) return; // منع التنفيذ المتعدد

        try
        {
            _isDeleteMessage = true;
            _message = $"هل أنت متأكد من حذف فاتورة رقم {obj.InvoiceNo.ToString()} ؟";
            bool? result = await mbox.ShowAsync();

            if (result == true)
            {
                SetButtonLoadingState("delete", true);

                var response = await _sell.Delete("Sells/deleteSell", obj.Id);
                if (response.State)
                {
                    ShowSuccessMessage("تم الحذف بنجاح");
                }
                else
                {
                    ShowErrorMessage(response.Message);
                }
            }
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "Delete",
                new Dictionary<string, object>
                {
                    ["SellId"] = obj.Id.ToString(),
                    ["InvoiceNo"] = obj.InvoiceNo.ToString()
                });
            ShowErrorMessage("حدث خطأ أثناء حذف الفاتورة. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            SetButtonLoadingState("delete", false);
        }
    }
    /// <summary>
    /// تحميل إعدادات المتجر الافتراضية
    /// </summary>
    private async Task LoadDefaultShopSettings()
    {
        try
        {
            // استخدام endpoint الصحيح للحصول على الإعدادات الافتراضية
            var response = await _shopSettings.GetByIdAsync("ShopSettings/getDefaultShopSettings", Guid.Empty);
            if (response.response == null && response.model != null)
            {
                _defaultShopSettings = response.model;
            }
            else
            {
                // محاولة جلب أول إعدادات متاحة إذا لم توجد إعدادات افتراضية
                var allSettingsResponse = await _shopSettings.GetAll("ShopSettings/getAllShopSettings");
                if (allSettingsResponse.response == null && allSettingsResponse.list != null && allSettingsResponse.list.Any())
                {
                    _defaultShopSettings = allSettingsResponse.list.First();
                }
                else
                {
                    // في حالة عدم وجود أي إعدادات، استخدم قيم افتراضية
                    _defaultShopSettings = new ShopSettingsDTO
                    {
                        StoreName = "اسم المتجر",
                        CompanyName = "اسم الشركة",
                        CompanyPhone = "رقم الهاتف",
                        StoreAddress = "عنوان المتجر"
                    };
                }
            }
        }
        catch (Exception ex)
        {
            _snackbar.Add($"خطأ في تحميل إعدادات المتجر: {ex.Message}", Severity.Warning);
            // استخدم قيم افتراضية في حالة الخطأ
            _defaultShopSettings = new ShopSettingsDTO
            {
                StoreName = "اسم المتجر",
                CompanyName = "اسم الشركة",
                CompanyPhone = "رقم الهاتف",
                StoreAddress = "عنوان المتجر"
            };
        }
    }

    /// <summary>
    /// إعادة تحميل إعدادات المتجر لضمان الحصول على أحدث البيانات
    /// </summary>
    private async Task RefreshShopSettings()
    {
        await LoadDefaultShopSettings();
    }

    /// <summary>
    /// طباعة فاتورة البيع باستخدام Print.js
    /// </summary>
    async Task Print()
    {
        // إعادة تحميل إعدادات المتجر قبل الطباعة لضمان الحصول على أحدث البيانات
        await RefreshShopSettings();
        await PrintWithFormat("auto");
    }

    /// <summary>
    /// طباعة فاتورة البيع بتنسيق محدد
    /// </summary>
    private async Task PrintWithFormat(string format)
    {
        if (_isLoading) return;

        // التحقق من صحة البيانات قبل الطباعة
        if (!ValidateSellForPrint())
            return;

        try
        {
            SetButtonLoadingState("print", true);
            _printButtonText = GetPrintButtonText(format);
            StateHasChanged();

            // التحقق من تحميل دوال JavaScript
            var isJsReady = await CheckJavaScriptReady();
            if (!isJsReady)
            {
                // محاولة استخدام طريقة بديلة
                await PrintUsingFallbackMethod(format);
                return;
            }

            // إنشاء HTML للفاتورة
            var sellHtml = GenerateSellInvoiceHtml(format);

            // استدعاء JavaScript لطباعة الفاتورة
            var printResult = await _iJSRuntime.InvokeAsync<bool>("printReceiptFromBlazor", sellHtml, format);

            if (printResult)
            {
                ShowSuccessMessage($"تم إرسال فاتورة البيع للطباعة بنجاح ({GetFormatDisplayName(format)})");
            }
            else
            {
                ShowErrorMessage("فشل في طباعة الفاتورة. تأكد من إعدادات الطابعة");
            }
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "Print",
                new Dictionary<string, object>
                {
                    ["SellId"] = id.ToString()
                });
            ShowErrorMessage($"خطأ في طباعة فاتورة البيع: {ex.Message}");
        }
        finally
        {
            SetButtonLoadingState("print", false);
            _printButtonText = "طباعة الفاتورة";
            StateHasChanged();
        }
    }

    private string GetExpDisplayText(Guid expId)
    {
        if (expId == Guid.Empty)
            return "اختر صلاحية";

        var exp = items.SelectMany(x => x.StoreItemExps).FirstOrDefault(x => x.Id == expId);
        if (exp?.Exp.HasValue == true)
        {
            return exp.Exp.Value.ToString("yyyy-MM-dd");
        }
        return "اختر صلاحية";
    }

    /// <summary>
    /// التنقل إلى الفاتورة السابقة
    /// </summary>
    private async Task NavigateToPreviousInvoice()
    {
        try
        {
            // التأكد من وجود فواتير
            if (!SellsNum.Any())
            {
                _snackbar.Add("لا توجد فواتير", Severity.Warning);
                return;
            }

            // إذا كان id ليس Guid.Empty نحاول جلب الفاتورة السابقة
            if (id != Guid.Empty)
            {
                int currentIndex = SellsNum.FindIndex(x => x.Id == id);
                if (currentIndex > 0)
                {
                    // الفاتورة السابقة تكون العنصر الذي قبله في القائمة
                    var previousInvoice = SellsNum[currentIndex - 1];
                    await GetSell(previousInvoice.Id);
                    _snackbar.Add($"تم الانتقال إلى الفاتورة رقم {previousInvoice.InvoiceNo}", Severity.Success);
                }
                else
                {
                    _snackbar.Add("لا توجد فاتورة سابقة", Severity.Info);
                }
            }
            else
            {
                // إذا كان id يساوي Guid.Empty، نجلب آخر فاتورة إن وجدت
                var lastInvoice = SellsNum.Last();
                await GetSell(lastInvoice.Id);
                _snackbar.Add($"تم الانتقال إلى الفاتورة رقم {lastInvoice.InvoiceNo}", Severity.Success);
            }

            // إعادة التركيز على حقل رقم الفاتورة
            await InvoiceNo.FocusAsync();
        }
        catch (Exception ex)
        {
            _snackbar.Add($"خطأ في التنقل: {ex.Message}", Severity.Error);
        }
        finally
        {
            StateHasChanged();
        }
    }

    /// <summary>
    /// التنقل إلى الفاتورة التالية
    /// </summary>
    private async Task NavigateToNextInvoice()
    {
        try
        {
            // التأكد من وجود فواتير
            if (!SellsNum.Any())
            {
                _snackbar.Add("لا توجد فواتير", Severity.Warning);
                return;
            }

            // إذا كان id ليس Guid.Empty نحاول جلب الفاتورة اللاحقة
            if (id != Guid.Empty)
            {
                int currentIndex = SellsNum.FindIndex(x => x.Id == id);
                if (currentIndex >= 0 && currentIndex < SellsNum.Count - 1)
                {
                    // الفاتورة اللاحقة تكون العنصر الذي يلي الفاتورة الحالية
                    var nextInvoice = SellsNum[currentIndex + 1];
                    await GetSell(nextInvoice.Id);
                    _snackbar.Add($"تم الانتقال إلى الفاتورة رقم {nextInvoice.InvoiceNo}", Severity.Success);
                }
                else
                {
                    _snackbar.Add("لا توجد فاتورة لاحقة", Severity.Info);
                }
            }
            else
            {
                // إذا كان id يساوي Guid.Empty، نجلب أول فاتورة إن وجدت
                var firstInvoice = SellsNum.First();
                await GetSell(firstInvoice.Id);
                _snackbar.Add($"تم الانتقال إلى الفاتورة رقم {firstInvoice.InvoiceNo}", Severity.Success);
            }

            // إعادة التركيز على حقل رقم الفاتورة
            await InvoiceNo.FocusAsync();
        }
        catch (Exception ex)
        {
            _snackbar.Add($"خطأ في التنقل: {ex.Message}", Severity.Error);
        }
        finally
        {
            StateHasChanged();
        }
    }

    /// <summary>
    /// التحقق من صحة رقم الفاتورة وضمان عدم السماح بالأرقام السالبة أو الصفر
    /// </summary>
    private void ValidateInvoiceNumber()
    {
        if (_Sell.InvoiceNo <= 0)
        {
            _Sell.InvoiceNo = 1;
            _snackbar.Add("رقم الفاتورة يجب أن يكون أكبر من الصفر", Severity.Warning);
            StateHasChanged();
        }
    }

    /// <summary>
    /// التحقق من صحة بيانات الفاتورة قبل الطباعة
    /// </summary>
    private bool ValidateSellForPrint()
    {
        if (_Sell.FinalTotal <= 0)
        {
            ShowErrorMessage("لا يمكن طباعة فاتورة بقيمة صفر أو سالبة");
            return false;
        }

        if (_Sell.Client == null)
        {
            ShowErrorMessage("يجب تحديد العميل قبل الطباعة");
            return false;
        }

        if (!_Sell.SellItemDTOs.Any())
        {
            ShowErrorMessage("لا يمكن طباعة فاتورة بدون أصناف");
            return false;
        }

        if (string.IsNullOrEmpty(_defaultShopSettings?.StoreName))
        {
            _snackbar.Add("تحذير: لم يتم تحميل إعدادات المتجر. سيتم استخدام قيم افتراضية", Severity.Warning);
        }

        return true;
    }

    /// <summary>
    /// معاينة فاتورة البيع قبل الطباعة
    /// </summary>
    private async Task PreviewSell()
    {
        try
        {
            // التحقق من وجود بيانات الفاتورة
            if (!ValidateSellForPrint())
                return;

            // إعادة تحميل إعدادات المتجر قبل المعاينة لضمان الحصول على أحدث البيانات
            await RefreshShopSettings();

            // التحقق من تحميل دوال JavaScript
            var isJsReady = await CheckJavaScriptReady();
            if (!isJsReady)
            {
                // استخدام طريقة بديلة للمعاينة
                await PreviewUsingFallbackMethod();
                return;
            }

            // إنشاء HTML للفاتورة
            var sellHtml = GenerateSellInvoiceHtml("preview");

            // استدعاء JavaScript لمعاينة الفاتورة
            var previewResult = await _iJSRuntime.InvokeAsync<bool>("previewReceiptFromBlazor", sellHtml, "auto");

            if (!previewResult)
            {
                ShowErrorMessage("فشل في فتح معاينة الفاتورة. تأكد من السماح للنوافذ المنبثقة");
            }
            else
            {
                _snackbar.Add("تم فتح معاينة الفاتورة في نافذة جديدة", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "PreviewSell",
                new Dictionary<string, object>
                {
                    ["SellId"] = id.ToString()
                });
            ShowErrorMessage($"خطأ في معاينة الفاتورة: {ex.Message}");
        }
    }

    /// <summary>
    /// إنشاء HTML لفاتورة البيع مع التصميم المحسن - تصميم A4 احترافي
    /// </summary>
    private string GenerateSellInvoiceHtml(string format = "auto")
    {
        var logoUrl = GetFullLogoUrl(_defaultShopSettings?.LogoPath);

        var currentDate = _Sell.Date.ToString("yyyy/MM/dd");
        var currentTime = DateTime.Now.ToString("HH:mm");
        var clientName = _Sell.Client?.Name ?? "غير محدد";
        var storeName = stores.FirstOrDefault(s => s.Id == _Sell.StoreId)?.Name ?? "غير محدد";

        // حساب الإجماليات
        var subtotal = _Sell.SellItemDTOs.Sum(item => item.Quantity * item.SalePrice);
        var totalAfterDiscount = _Sell.FinalTotal;
        var discountAmount = subtotal - totalAfterDiscount;

        return $@"
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

                body {{
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 0;
                    background: white;
                    font-size: 12px;
                    line-height: 1.3;
                }}

                .invoice-container {{
                    width: 190mm;
                    max-height: 277mm;
                    margin: 0 auto;
                    background: #ffffff;
                    border: 1px solid #1e40af;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    padding: 8mm;
                    page-break-inside: avoid;
                    overflow: hidden;
                }}

                /* الشريط العلوي الأزرق */
                .top-blue-stripe {{
                    height: 4px;
                    background: linear-gradient(90deg, #1e40af 0%, #3b82f6 60%, transparent 100%);
                    width: 60%;
                    margin-bottom: 8px;
                }}

                /* الهيدر */
                .invoice-header {{
                    padding: 10px 15px;
                    position: relative;
                    background: #ffffff;
                    border-bottom: 1px solid #e2e8f0;
                    margin-bottom: 12px;
                    page-break-inside: avoid;
                    min-height: 60px;
                }}

                /* شعار الشركة في الزاوية اليمنى */
                .company-logo {{
                    position: absolute;
                    top: 10px;
                    right: 15px;
                    width: 50px;
                    height: 50px;
                    background: #1e40af;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                    text-align: center;
                    line-height: 1.1;
                    border-radius: 4px;
                }}

                /* معلومات الشركة في الوسط */
                .company-info {{
                    text-align: center;
                    margin: 0 70px;
                    padding-top: 5px;
                }}

                .store-name {{
                    font-size: 16px;
                    font-weight: 700;
                    color: #1e293b;
                    margin-bottom: 4px;
                }}

                .company-details {{
                    font-size: 10px;
                    color: #64748b;
                    line-height: 1.2;
                }}

                /* التاريخ في الزاوية اليسرى */
                .date-section {{
                    position: absolute;
                    top: 10px;
                    left: 15px;
                    background: #1e40af;
                    color: white;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: 600;
                    text-align: center;
                    min-width: 70px;
                }}

                /* عنوان الفاتورة */
                .invoice-title {{
                    text-align: center;
                    font-size: 18px;
                    font-weight: 700;
                    color: #1e40af;
                    margin: 15px 0 10px 0;
                    padding-bottom: 8px;
                    border-bottom: 2px solid #1e40af;
                    page-break-inside: avoid;
                }}

                .invoice-number {{
                    text-align: center;
                    font-size: 12px;
                    color: #64748b;
                    margin-bottom: 12px;
                }}

                /* معلومات الفاتورة */
                .invoice-info {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    margin-bottom: 12px;
                    padding: 8px;
                    background: #f8fafc;
                    border-radius: 4px;
                    border: 1px solid #e2e8f0;
                    page-break-inside: avoid;
                }}

                .info-item {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 3px 0;
                }}

                .info-label {{
                    font-size: 11px;
                    color: #64748b;
                    font-weight: 500;
                }}

                .info-value {{
                    font-size: 11px;
                    color: #1e293b;
                    font-weight: 600;
                }}

                /* جدول الأصناف */
                .items-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 12px;
                    background: white;
                    border: 1px solid #e2e8f0;
                    page-break-inside: avoid;
                }}

                .items-table th {{
                    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
                    color: white;
                    padding: 6px 4px;
                    text-align: center;
                    font-weight: 600;
                    font-size: 10px;
                    border-bottom: 1px solid #1e40af;
                }}

                .items-table td {{
                    padding: 4px 3px;
                    text-align: center;
                    border-bottom: 1px solid #e2e8f0;
                    font-size: 9px;
                    color: #1e293b;
                    line-height: 1.2;
                }}

                .items-table tr:nth-child(even) {{
                    background: #f8fafc;
                }}

                .items-table tbody {{
                    page-break-inside: auto;
                }}

                .items-table tr {{
                    page-break-inside: avoid;
                }}

                /* قسم الإجماليات */
                .totals-section {{
                    margin-top: 8px;
                    padding: 8px;
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    border-radius: 4px;
                    border: 1px solid #1e40af;
                    page-break-inside: avoid;
                }}

                .totals-grid {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 6px;
                }}

                .total-item {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 4px 8px;
                    background: white;
                    border-radius: 3px;
                    border: 1px solid #d1d5db;
                }}

                .total-label {{
                    font-size: 10px;
                    color: #374151;
                    font-weight: 500;
                }}

                .total-value {{
                    font-size: 10px;
                    color: #1e293b;
                    font-weight: 700;
                }}

                .final-total {{
                    grid-column: 1 / -1;
                    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
                    color: white !important;
                    border: none !important;
                }}

                .final-total .total-label,
                .final-total .total-value {{
                    color: white !important;
                    font-weight: 700 !important;
                    font-size: 11px !important;
                }}

                /* الفوتر */
                .invoice-footer {{
                    margin-top: 8px;
                    padding-top: 8px;
                    border-top: 1px solid #e2e8f0;
                    text-align: center;
                    font-size: 9px;
                    color: #64748b;
                    page-break-inside: avoid;
                }}

                /* تنسيقات الطباعة */
                @media print {{
                    * {{
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        box-sizing: border-box !important;
                    }}

                    body {{
                        margin: 0 !important;
                        padding: 0 !important;
                        background: white !important;
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        font-size: 12px !important;
                    }}

                    .invoice-container {{
                        width: 190mm !important;
                        max-height: 277mm !important;
                        height: auto !important;
                        min-height: auto !important;
                        margin: 0 !important;
                        padding: 10mm !important;
                        background: white !important;
                        box-shadow: none !important;
                        border: 1px solid #000000 !important;
                        page-break-after: auto !important;
                        page-break-inside: avoid !important;
                        overflow: hidden !important;
                    }}

                    /* منع كسر الصفحة داخل العناصر المهمة */
                    .invoice-header,
                    .invoice-title,
                    .invoice-info,
                    .totals-section,
                    .invoice-footer {{
                        page-break-inside: avoid !important;
                    }}

                    /* السماح بكسر الصفحة في الجدول إذا لزم الأمر */
                    .items-table {{
                        page-break-inside: auto !important;
                    }}

                    .items-table tr {{
                        page-break-inside: avoid !important;
                    }}

                    /* إخفاء أزرار الطباعة والإغلاق */
                    .mud-button, .mud-fab, .mud-dialog-actions,
                    .print-button, .close-button, .preview-button,
                    button, .btn, [role=""button""] {{
                        display: none !important;
                    }}

                    @page {{
                        size: A4 portrait !important;
                        margin: 10mm !important;
                    }}
                }}

                /* للعرض على الشاشة */
                @media screen {{
                    .invoice-container {{
                        transform: scale(0.8);
                        transform-origin: top center;
                        margin: 20px auto;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    }}
                }}
            </style>

            <div class='invoice-container'>
                <!-- الشريط الأزرق العلوي -->
                <div class='top-blue-stripe'></div>

                <div class='invoice-header'>
                    <!-- شعار الشركة -->
                    <div class='company-logo'>
                        {(!string.IsNullOrEmpty(logoUrl) ? $"<img src='{logoUrl}' alt='شعار' style='width:100%;height:100%;object-fit:contain;' onerror='this.parentElement.innerHTML=\"شعار<br>الشركة\"' />" : "شعار<br>الشركة")}
                    </div>

                    <!-- التاريخ -->
                    <div class='date-section'>
                        التاريخ: {currentDate}
                    </div>

                    <!-- معلومات الشركة -->
                    <div class='company-info'>
                        <div class='store-name'>{_defaultShopSettings?.StoreName ?? "اسم المتجر"}</div>
                        <div class='company-details'>
                            {_defaultShopSettings?.StoreAddress ?? "عنوان المتجر"}<br>
                            هاتف: {_defaultShopSettings?.CompanyPhone ?? "رقم الهاتف"}
                        </div>
                    </div>
                </div>

                <!-- عنوان الفاتورة -->
                <div class='invoice-title'>فاتورة بيع</div>
                <div class='invoice-number'>رقم الفاتورة: {_Sell.InvoiceNo}</div>

                <!-- معلومات الفاتورة -->
                <div class='invoice-info'>
                    <div class='info-item'>
                        <span class='info-label'>العميل:</span>
                        <span class='info-value'>{clientName}</span>
                    </div>
                    <div class='info-item'>
                        <span class='info-label'>المخزن:</span>
                        <span class='info-value'>{storeName}</span>
                    </div>
                    <div class='info-item'>
                        <span class='info-label'>تاريخ الفاتورة:</span>
                        <span class='info-value'>{currentDate}</span>
                    </div>
                    <div class='info-item'>
                        <span class='info-label'>وقت الطباعة:</span>
                        <span class='info-value'>{currentTime}</span>
                    </div>
                </div>

                <!-- جدول الأصناف -->
                <table class='items-table'>
                    <thead>
                        <tr>
                            <th style='width: 5%;'>#</th>
                            <th style='width: 25%;'>الصنف</th>
                            <th style='width: 10%;'>الوحدة</th>
                            <th style='width: 10%;'>الكمية</th>
                            <th style='width: 12%;'>السعر</th>
                            <th style='width: 10%;'>التخفيض</th>
                            <th style='width: 12%;'>السعر بعد التخفيض</th>
                            <th style='width: 16%;'>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {string.Join("", _Sell.SellItemDTOs.Select((item, index) => $@"
                        <tr>
                            <td>{index + 1}</td>
                            <td style='text-align: right; padding-right: 10px;'>{item.StoreItemExp?.StoreItem?.Item?.Name ?? "غير محدد"}</td>
                            <td>{item.ItemUnit?.Unit?.Name ?? "غير محدد"}</td>
                            <td>{item.Quantity:N2}</td>
                            <td>{item.SalePrice:N2}</td>
                            <td>{(item.SalePrice - item.SalePriceAfterDiscount):N2}</td>
                            <td>{item.SalePriceAfterDiscount:N2}</td>
                            <td style='font-weight: 600;'>{(item.Quantity * item.SalePriceAfterDiscount):N2}</td>
                        </tr>"))}
                    </tbody>
                </table>

                <!-- قسم الإجماليات -->
                <div class='totals-section'>
                    <div class='totals-grid'>
                        <div class='total-item'>
                            <span class='total-label'>الإجمالي قبل التخفيض:</span>
                            <span class='total-value'>{subtotal:N2} ريال</span>
                        </div>
                        <div class='total-item'>
                            <span class='total-label'>قيمة التخفيض:</span>
                            <span class='total-value'>{discountAmount:N2} ريال</span>
                        </div>
                        <div class='total-item'>
                            <span class='total-label'>المدفوع:</span>
                            <span class='total-value'>{_Sell.Paid:N2} ريال</span>
                        </div>
                        <div class='total-item'>
                            <span class='total-label'>المتبقي:</span>
                            <span class='total-value'>{(_Sell.FinalTotal - _Sell.Paid):N2} ريال</span>
                        </div>
                        <div class='total-item final-total'>
                            <span class='total-label'>الإجمالي النهائي:</span>
                            <span class='total-value'>{totalAfterDiscount:N2} ريال</span>
                        </div>
                    </div>
                </div>

                <!-- الفوتر -->
                <div class='invoice-footer'>
                    <p>شكراً لتعاملكم معنا</p>
                    <p>{_defaultShopSettings?.CompanyName ?? "اسم الشركة"} - {_defaultShopSettings?.CompanyPhone ?? "رقم الهاتف"}</p>
                </div>
            </div>";
    }

    /// <summary>
    /// التحقق من تحميل دوال JavaScript المطلوبة
    /// </summary>
    private async Task<bool> CheckJavaScriptReady()
    {
        try
        {
            // التحقق من وجود دالة الطباعة
            var printFunctionExists = await _iJSRuntime.InvokeAsync<bool>("eval", "typeof printReceiptFromBlazor === 'function'");

            // التحقق من وجود دالة المعاينة
            var previewFunctionExists = await _iJSRuntime.InvokeAsync<bool>("eval", "typeof previewReceiptFromBlazor === 'function'");

            return printFunctionExists && previewFunctionExists;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// طباعة باستخدام طريقة بديلة عند عدم تحميل مكتبة Print.js
    /// </summary>
    private async Task PrintUsingFallbackMethod(string format)
    {
        try
        {
            // إنشاء HTML للفاتورة
            var sellHtml = GenerateSellInvoiceHtml(format);

            // إنشاء صفحة HTML كاملة للطباعة
            var fullHtml = $@"
                <!DOCTYPE html>
                <html dir='rtl' lang='ar'>
                <head>
                    <meta charset='UTF-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>طباعة فاتورة البيع</title>
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
                        * {{
                            box-sizing: border-box;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }}
                        body {{
                            font-family: 'Noto Sans Arabic', Arial, sans-serif;
                            direction: rtl;
                            margin: 0;
                            padding: 20px;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }}
                        @media print {{
                            * {{
                                -webkit-print-color-adjust: exact !important;
                                print-color-adjust: exact !important;
                                color-adjust: exact !important;
                            }}
                            body {{ margin: 0 !important; padding: 0 !important; background: white !important; }}
                            @page {{ size: A4 portrait !important; margin: 10mm !important; }}
                        }}
                    </style>
                </head>
                <body>
                    {sellHtml}
                    <script>
                        window.onload = function() {{
                            setTimeout(function() {{
                                window.print();
                            }}, 500);
                        }};
                    </script>
                </body>
                </html>";

            // فتح نافذة جديدة للطباعة
            await _iJSRuntime.InvokeVoidAsync("eval", $@"
                var printWindow = window.open('', '_blank', 'width=800,height=600');
                printWindow.document.write(`{fullHtml.Replace("`", "\\`")}`);
                printWindow.document.close();
            ");

            ShowSuccessMessage("تم فتح نافذة الطباعة (طريقة بديلة)");
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "PrintUsingFallbackMethod",
                new Dictionary<string, object>
                {
                    ["SellId"] = id.ToString(),
                    ["Format"] = format
                });
            ShowErrorMessage($"خطأ في الطباعة البديلة: {ex.Message}");
        }
    }

    /// <summary>
    /// معاينة باستخدام طريقة بديلة
    /// </summary>
    private async Task PreviewUsingFallbackMethod()
    {
        try
        {
            // إنشاء HTML للفاتورة
            var sellHtml = GenerateSellInvoiceHtml("preview");

            // إنشاء صفحة HTML كاملة للمعاينة
            var fullHtml = $@"
                <!DOCTYPE html>
                <html dir='rtl' lang='ar'>
                <head>
                    <meta charset='UTF-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>معاينة فاتورة البيع</title>
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
                        * {{
                            box-sizing: border-box;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }}
                        body {{
                            font-family: 'Noto Sans Arabic', Arial, sans-serif;
                            direction: rtl;
                            margin: 0;
                            padding: 20px;
                            background: #f5f5f5;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }}
                        .actions {{ text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; }}
                        .btn {{ padding: 10px 20px; margin: 0 5px; border: none; border-radius: 4px; cursor: pointer; font-family: 'Noto Sans Arabic', Arial, sans-serif; }}
                        .btn-print {{ background: #007bff; color: white; }}
                        .btn-close {{ background: #6c757d; color: white; }}
                        @media print {{
                            .actions {{ display: none !important; }}
                            @page {{ size: A4 portrait !important; margin: 10mm !important; }}
                        }}
                    </style>
                </head>
                <body>
                    {sellHtml}
                    <div class='actions'>
                        <button class='btn btn-print' onclick='window.print()'>طباعة</button>
                        <button class='btn btn-close' onclick='window.close()'>إغلاق</button>
                    </div>
                </body>
                </html>";

            // فتح نافذة جديدة للمعاينة
            await _iJSRuntime.InvokeVoidAsync("eval", $@"
                var previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
                previewWindow.document.write(`{fullHtml.Replace("`", "\\`")}`);
                previewWindow.document.close();
            ");

            ShowSuccessMessage("تم فتح معاينة الفاتورة (طريقة بديلة)");
        }
        catch (Exception ex)
        {
            await _errorLogging.LogExceptionAsync(ex, "PreviewUsingFallbackMethod",
                new Dictionary<string, object>
                {
                    ["SellId"] = id.ToString()
                });
            ShowErrorMessage($"خطأ في المعاينة البديلة: {ex.Message}");
        }
    }

    /// <summary>
    /// الحصول على نص زر الطباعة حسب التنسيق
    /// </summary>
    private string GetPrintButtonText(string format)
    {
        return "جاري طباعة A4...";
    }

    /// <summary>
    /// الحصول على اسم التنسيق للعرض
    /// </summary>
    private string GetFormatDisplayName(string format)
    {
        return "A4";
    }

    /// <summary>
    /// تحويل مسار الشعار النسبي إلى URL كامل للـ API
    /// </summary>
    /// <param name="logoPath">مسار الشعار النسبي</param>
    /// <returns>URL كامل للصورة</returns>
    private string GetFullLogoUrl(string? logoPath)
    {
        if (string.IsNullOrEmpty(logoPath))
        {
            return string.Empty;
        }

        // إذا كان المسار يبدأ بـ http أو https، فهو مسار كامل
        if (logoPath.StartsWith("http://") || logoPath.StartsWith("https://"))
        {
            return logoPath;
        }

        // إذا كان المسار يبدأ بـ data:، فهو base64
        if (logoPath.StartsWith("data:"))
        {
            return logoPath;
        }

        // URL الأساسي للـ API (يجب أن يتطابق مع إعدادات النظام)
        var apiBaseUrl = "https://localhost:7282";

        // تحويل المسار النسبي إلى URL كامل للـ API
        // مثال: /logo/image.png -> https://localhost:7282/logo/image.png
        var fullPath = logoPath.StartsWith("/") ? logoPath : "/" + logoPath;
        return apiBaseUrl + fullPath;
    }
}